<html>
<head>
    <title> Seatwork 2 </title>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"> </script>

    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .form-container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        
        input[type="text"]:focus {
            outline: none;
            border-color: #4CAF50;
        }
        
        .verify-button {
            background-color: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            width: 100%;
            margin-top: 20px;
            transition: background-color 0.3s;
        }
        
        .verify-button:hover {
            background-color: #45a049;
        }
        
        .verify-button:active {
            background-color: #3d8b40;
        }
        
        .confirmation-message {
            display: none;
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            margin-top: 20px;
            text-align: center;
        }
        
        .required {
            color: red;
        }

        .customer-display {
            background-color: #e8f5e8;
            border: 2px solid #4CAF50;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
            display: none;
        }

        .customer-display h2 {
            color: #2e7d32;
            margin-top: 0;
            margin-bottom: 20px;
            text-align: center;
        }

        .info-section {
            background-color: white;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #c8e6c9;
        }

        .info-item {
            margin-bottom: 15px;
            font-size: 16px;
        }

        .info-item:last-child {
            margin-bottom: 0;
        }

        .info-item strong {
            color: #2e7d32;
            display: inline-block;
            width: 120px;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h1> Seatwork 2 </h1>
        
        <form id="customerForm">
            <div class="form-group">
                <label for="lastName">Last Name <span class="required">*</span></label>
                <input type="text" id="lastName" name="lastName" required>
            </div>
            
            <div class="form-group">
                <label for="firstName">First Name <span class="required">*</span></label>
                <input type="text" id="firstName" name="firstName" required>
            </div>
            
            <div class="form-group">
                <label for="middleName">Middle Name</label>
                <input type="text" id="middleName" name="middleName">
            </div>
            
            <div class="form-group">
                <label for="address">Address <span class="required">*</span></label>
                <input type="text" id="address" name="address" required>
            </div>
            
            <button type="button" class="verify-button">
                Verify Entries
            </button>
        </form>
        
        <div id="confirmationMessage" class="confirmation-message">
            ✓ All entries have been verified and confirmed!
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Function to process customer data using jQuery
            function processCustomerData() {
                // Get form data using jQuery selectors
                const lastName = $('#lastName').val().trim();
                const firstName = $('#firstName').val().trim();
                const middleName = $('#middleName').val().trim();
                const address = $('#address').val().trim();

                // Validate required fields
                if (!lastName || !firstName || !address) {
                    alert('Please fill in all required fields (Last Name, First Name, and Address).');
                    return false;
                }

                // Create full name
                let fullName = firstName;
                if (middleName) {
                    fullName += ' ' + middleName;
                }
                fullName += ' ' + lastName;

                // Display the customer information
                displayCustomerInfo(fullName, address);

                return true;
            }

            // Function to display customer information
            function displayCustomerInfo(fullName, address) {
                // Create or update the display area
                let displayArea = $('#customerDisplay');

                if (displayArea.length === 0) {
                    // Create display area if it doesn't exist
                    const displayHTML = `
                        <div id="customerDisplay" class="customer-display">
                            <h2>Here is the summary of your data</h2>
                            <div class="info-section">
                                <div class="info-item">
                                    <strong>Customer Name:</strong>
                                    <span id="displayFullName"></span>
                                </div>
                                <div class="info-item">
                                    <strong>Address:</strong>
                                    <span id="displayAddress"></span>
                                </div>
                            </div>
                        </div>
                    `;

                    // Insert after the confirmation message
                    $('#confirmationMessage').after(displayHTML);
                    displayArea = $('#customerDisplay');
                }

                // Update the displayed information using jQuery
                $('#displayFullName').text(fullName);
                $('#displayAddress').text(address);

                // Show the display area and confirmation message with jQuery animation
                $('#confirmationMessage').slideDown(300);
                displayArea.slideDown(300);

                // Log to console for verification
                console.log('Customer Data Processed with jQuery:');
                console.log('Full Name:', fullName);
                console.log('Address:', address);
            }

            // jQuery event handler for the verify button
            $('.verify-button').on('click', function() {
                const success = processCustomerData();

                if (success) {
                    // Change button appearance temporarily using jQuery
                    const $button = $(this);
                    const originalText = $button.text();

                    $button.text('Processed ✓')
                           .css('background-color', '#28a745');

                    setTimeout(() => {
                        $button.text(originalText)
                               .css('background-color', '#4CAF50');
                    }, 2000);
                }
            });

            // Hide display when user starts typing using jQuery
            $('#customerForm input').on('input', function() {
                $('#customerDisplay').slideUp(300);
                $('#confirmationMessage').slideUp(300);
            });
        });
    </script>
</body>
</html>