<html>
<head>
    <title> Seatwork 2 </title>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"> </script>


</head>
<body>
    <h1> Seatwork 2 </h1>

    <form id="customerForm">
        <input type="text" id="lastName" name="lastName" placeholder="Type Your Last Name" required>
        <br><br>

        <input type="text" id="firstName" name="firstName" placeholder="Type Your First Name" required>
        <br><br>

        <input type="text" id="middleName" name="middleName" placeholder="Type Your Middle Name">
        <br><br>

        <input type="text" id="address" name="address" placeholder="Type Your Address" required>
        <br><br>

        <button type="button" class="verify-button">
            Ok
        </button>
    </form>

    <script>
        $(document).ready(function() {
            // Function to process customer data using jQuery
            function processCustomerData() {
                // Get form data using jQuery selectors
                const lastName = $('#lastName').val().trim();
                const firstName = $('#firstName').val().trim();
                const middleName = $('#middleName').val().trim();
                const address = $('#address').val().trim();

                // Validate required fields
                if (!lastName || !firstName || !address) {
                    alert('Please fill in all required fields (Last Name, First Name, and Address).');
                    return false;
                }

                // Create full name
                let fullName = firstName;
                if (middleName) {
                    fullName += ' ' + middleName;
                }
                fullName += ' ' + lastName;

                // Display the customer information
                displayCustomerInfo(fullName, address);

                return true;
            }

            // Function to display customer information
            function displayCustomerInfo(fullName, address) {
                // Create or update the display area
                let displayArea = $('#customerDisplay');

                if (displayArea.length === 0) {
                    // Create display area if it doesn't exist
                    const displayHTML = `
                        <div id="customerDisplay">
                            <h2>Here is the summary of your data</h2>
                            <p><strong>Customer Name:</strong> <span id="displayFullName"></span></p>
                            <p><strong>Address:</strong> <span id="displayAddress"></span></p>
                        </div>
                    `;

                    // Insert after the form
                    $('#customerForm').after(displayHTML);
                    displayArea = $('#customerDisplay');
                }

                // Update the displayed information using jQuery
                $('#displayFullName').text(fullName);
                $('#displayAddress').text(address);

                // Show the display area with jQuery animation
                displayArea.slideDown(300);

                // Log to console for verification
                console.log('Customer Data Processed with jQuery:');
                console.log('Full Name:', fullName);
                console.log('Address:', address);
            }

            // jQuery event handler for the verify button
            $('.verify-button').on('click', function() {
                const success = processCustomerData();

                if (success) {
                    // Change button appearance temporarily using jQuery
                    const $button = $(this);
                    const originalText = $button.text();

                    $button.text('Processed ✓')
                           .css('background-color', '#28a745');

                    setTimeout(() => {
                        $button.text(originalText)
                               .css('background-color', '#4CAF50');
                    }, 2000);
                }
            });

            // Hide display when user starts typing using jQuery
            $('#customerForm input').on('input', function() {
                $('#customerDisplay').slideUp(300);
            });
        });
    </script>
</body>
</html>