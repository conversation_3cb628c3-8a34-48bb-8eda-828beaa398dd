<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Data Entry Form</title>
    <!-- jQuery CDN -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .form-container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        
        input[type="text"]:focus {
            outline: none;
            border-color: #4CAF50;
        }
        
        .verify-button {
            background-color: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            width: 100%;
            margin-top: 20px;
            transition: background-color 0.3s;
        }
        
        .verify-button:hover {
            background-color: #45a049;
        }
        
        .verify-button:active {
            background-color: #3d8b40;
        }
        
        .confirmation-message {
            display: none;
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            margin-top: 20px;
            text-align: center;
        }
        
        .required {
            color: red;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h1>Customer Data Entry Form</h1>
        
        <form id="customerForm">
            <div class="form-group">
                <label for="lastName">Last Name <span class="required">*</span></label>
                <input type="text" id="lastName" name="lastName" required>
            </div>
            
            <div class="form-group">
                <label for="firstName">First Name <span class="required">*</span></label>
                <input type="text" id="firstName" name="firstName" required>
            </div>
            
            <div class="form-group">
                <label for="middleName">Middle Name</label>
                <input type="text" id="middleName" name="middleName">
            </div>
            
            <div class="form-group">
                <label for="address">Address <span class="required">*</span></label>
                <input type="text" id="address" name="address" required>
            </div>
            
            <button type="button" class="verify-button" onclick="verifyEntries()">
                Verify Entries
            </button>
        </form>
        
        <div id="confirmationMessage" class="confirmation-message">
            ✓ All entries have been verified and confirmed!
        </div>
    </div>

    <script>
        function verifyEntries() {
            // Get form elements
            const lastName = document.getElementById('lastName').value.trim();
            const firstName = document.getElementById('firstName').value.trim();
            const middleName = document.getElementById('middleName').value.trim();
            const address = document.getElementById('address').value.trim();
            
            // Check if required fields are filled
            if (!lastName || !firstName || !address) {
                alert('Please fill in all required fields (Last Name, First Name, and Address).');
                return;
            }
            
            // Display confirmation message
            const confirmationDiv = document.getElementById('confirmationMessage');
            confirmationDiv.style.display = 'block';
            
            // Optional: Log the data to console for verification
            console.log('Customer Data Verified:');
            console.log('Last Name:', lastName);
            console.log('First Name:', firstName);
            console.log('Middle Name:', middleName || 'Not provided');
            console.log('Address:', address);
            
            // Change button text temporarily to show action was completed
            const button = document.querySelector('.verify-button');
            const originalText = button.textContent;
            button.textContent = 'Verified ✓';
            button.style.backgroundColor = '#28a745';
            
            setTimeout(() => {
                button.textContent = originalText;
                button.style.backgroundColor = '#4CAF50';
            }, 2000);
        }
        
        // Hide confirmation message when user starts typing again
        document.addEventListener('input', function() {
            const confirmationDiv = document.getElementById('confirmationMessage');
            if (confirmationDiv.style.display === 'block') {
                confirmationDiv.style.display = 'none';
            }
        });
    </script>
</body>
</html>
