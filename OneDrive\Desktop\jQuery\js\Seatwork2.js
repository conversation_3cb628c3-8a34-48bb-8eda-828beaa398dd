$(document).ready(function() {
    // Function to get customer data and display it
    function processCustomerData() {
        // Get form data using jQuery
        const lastName = $('#lastName').val().trim();
        const firstName = $('#firstName').val().trim();
        const middleName = $('#middleName').val().trim();
        const address = $('#address').val().trim();
        
        // Validate required fields
        if (!lastName || !firstName || !address) {
            alert('Please fill in all required fields (Last Name, First Name, and Address).');
            return false;
        }
        
        // Create full name
        let fullName = firstName;
        if (middleName) {
            fullName += ' ' + middleName;
        }
        fullName += ' ' + lastName;
        
        // Display the processed data
        displayCustomerInfo(fullName, address);
        
        return true;
    }
    
    // Function to display customer information
    function displayCustomerInfo(fullName, address) {
        // Create or update the display area
        let displayArea = $('#customerDisplay');
        
        if (displayArea.length === 0) {
            // Create display area if it doesn't exist
            const displayHTML = `
                <div id="customerDisplay" class="customer-display">
                    <h2>Customer Information</h2>
                    <div class="info-section">
                        <div class="info-item">
                            <strong>Full Name:</strong>
                            <span id="displayFullName"></span>
                        </div>
                        <div class="info-item">
                            <strong>Address:</strong>
                            <span id="displayAddress"></span>
                        </div>
                    </div>
                </div>
            `;
            
            // Insert after the form
            $('#customerForm').after(displayHTML);
            displayArea = $('#customerDisplay');
        }
        
        // Update the displayed information
        $('#displayFullName').text(fullName);
        $('#displayAddress').text(address);
        
        // Show the display area with animation
        displayArea.slideDown(300);
        
        // Show success message
        $('#confirmationMessage').slideDown(300);
        
        // Log to console for debugging
        console.log('Customer Data Processed:');
        console.log('Full Name:', fullName);
        console.log('Address:', address);
    }
    
    // Function to clear the display
    function clearDisplay() {
        $('#customerDisplay').slideUp(300);
        $('#confirmationMessage').slideUp(300);
    }
    
    // Replace the original verify button functionality
    $('.verify-button').off('click').on('click', function() {
        const success = processCustomerData();
        
        if (success) {
            // Change button appearance temporarily
            const $button = $(this);
            const originalText = $button.text();
            
            $button.text('Processed ✓')
                   .css('background-color', '#28a745');
            
            setTimeout(() => {
                $button.text(originalText)
                       .css('background-color', '#4CAF50');
            }, 2000);
        }
    });
    
    // Add a clear button
    if ($('#clearButton').length === 0) {
        const clearButtonHTML = `
            <button type="button" id="clearButton" class="clear-button">
                Clear Display
            </button>
        `;
        $('.verify-button').after(clearButtonHTML);
    }
    
    // Clear button functionality
    $('#clearButton').on('click', function() {
        clearDisplay();
        
        // Optional: Clear form fields
        if (confirm('Do you want to clear the form fields as well?')) {
            $('#customerForm')[0].reset();
        }
    });
    
    // Hide display when user starts typing in form fields
    $('#customerForm input').on('input', function() {
        if ($('#customerDisplay').is(':visible')) {
            clearDisplay();
        }
    });
    
    // Add keyboard shortcut (Ctrl+Enter) to process data
    $(document).on('keydown', function(e) {
        if (e.ctrlKey && e.which === 13) { // Ctrl + Enter
            e.preventDefault();
            processCustomerData();
        }
    });
    
    // Add form validation styling
    $('#customerForm input[required]').on('blur', function() {
        const $input = $(this);
        if ($input.val().trim() === '') {
            $input.css('border-color', '#dc3545');
        } else {
            $input.css('border-color', '#28a745');
        }
    });
    
    // Reset border color on focus
    $('#customerForm input').on('focus', function() {
        $(this).css('border-color', '#4CAF50');
    });
});

// Global function to manually trigger data processing (for external use)
function getCustomerData() {
    const customerData = {
        lastName: $('#lastName').val().trim(),
        firstName: $('#firstName').val().trim(),
        middleName: $('#middleName').val().trim(),
        address: $('#address').val().trim(),
        fullName: function() {
            let name = this.firstName;
            if (this.middleName) {
                name += ' ' + this.middleName;
            }
            name += ' ' + this.lastName;
            return name;
        }
    };
    
    return customerData;
}