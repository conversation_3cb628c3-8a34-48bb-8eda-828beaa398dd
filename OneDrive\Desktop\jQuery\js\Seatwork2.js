$(document).ready(function() {
    // Function to get and process customer data
    function processCustomerData() {
        // Get form data using jQuery selectors
        const lastName = $('#lastName').val();
        const firstName = $('#firstName').val();
        const middleName = $('#middleName').val();
        const address = $('#address').val();

        // Create full name
        let fullName = firstName;
        if (middleName) {
            fullName += ' ' + middleName;
        }
        fullName += ' ' + lastName;

        // Display data in output section using <p> elements
        $('#output').empty();
        $('#output').append('<p><strong>Customer Name:</strong> ' + fullName + '</p>');
        $('#output').append('<p><strong>Address:</strong> ' + address + '</p>');

        // Alternative method using single append
        // $('#output').append(
        //     '<p><strong>Customer Name:</strong> ' + fullName + '</p>' +
        //     '<p><strong>Address:</strong> ' + address + '</p>'
        // );
    }

    // Button click event handler
    $('#processBtn').on('click', function() {
        processCustomerData();
    });

    // Alternative: Process data when any input changes
    $('#customerForm input').on('input', function() {
        processCustomerData();
    });

    // Clear output function
    function clearOutput() {
        $('#output').empty();
        $('#output').append('<p>No data to display</p>');
    }

    // Clear button event handler
    $('#clearBtn').on('click', function() {
        clearOutput();
        $('#customerForm')[0].reset();
    });
});